{% sw_extends "@Storefront/storefront/component/payment/payment-method.html.twig" %}

{% block component_payment_method_field %}
    {% if payment and payment.id == constant("UnzerPayment6\\Installer\\PaymentInstaller::PAYMENT_ID_APPLE_PAY") %}
        <div class="unzer-payment-apple-pay-method-wrapper">
            {{ parent() }}
        </div>
    {% elseif payment and payment.id == constant("UnzerPayment6\\Installer\\PaymentInstaller::PAYMENT_ID_APPLE_PAY_V2") %}
        <div class="unzer-payment-apple-pay-v2-method-wrapper">
            {{ parent() }}
        </div>
    {% elseif payment and payment.id == constant("UnzerPayment6\\Installer\\PaymentInstaller::PAYMENT_ID_GOOGLE_PAY") %}
        <div class="unzer-payment-google-pay-method-wrapper">
            {{ parent() }}
        </div>
    {% else %}
        {{ parent() }}
    {% endif %}
{% endblock %}

{% block component_payment_method_description %}
    {{ parent() }}
    {% if payment and payment.id == constant("UnzerPayment6\\Installer\\PaymentInstaller::PAYMENT_ID_OPEN_BANKING") and page.extensions.unzerPaymentData.publicKey and payment.id is same as(selectedPaymentMethodId)%}
        <unzer-payment
                publicKey="{{ page.extensions.unzerPaymentData.publicKey }}"
                locale="{{ page.extensions.unzerPaymentData.locale }}">
            <unzer-open-banking></unzer-open-banking>
        </unzer-payment>
    {% endif %}

    {% if payment and payment.id == constant("UnzerPayment6\\Installer\\PaymentInstaller::PAYMENT_ID_GOOGLE_PAY") and page.extensions.unzerPaymentData.publicKey and payment.id is same as(selectedPaymentMethodId) %}
        {% set googlePayExtensionName = 'unzerGooglePay' %}
        {% if page.extensions[googlePayExtensionName] %}
            {% set googlePayOptions = page.extensions[googlePayExtensionName].publicConfig|merge({
                currency: context.currency.isoCode,
                amount: page.cart.price.rawTotal ?: page.cart.price.totalPrice
            }) %}
            <div data-unzer-payment-google-pay data-unzer-payment-google-pay-options="{{ googlePayOptions|json_encode }}">
                <div id="unzer-google-pay-button"></div>
            </div>
        {% endif %}
    {% endif %}

{% endblock %}
