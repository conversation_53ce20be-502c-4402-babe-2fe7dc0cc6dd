{% sw_extends '@Storefront/storefront/component/payment/payment-fields.html.twig' %}

{% set expressCheckoutData = page.cart.extensions[constant('Swag\\PayPal\\Checkout\\ExpressCheckout\\SalesChannel\\ExpressPrepareCheckoutRoute::PAYPAL_EXPRESS_CHECKOUT_CART_EXTENSION_ID')] %}
{% set collapseTriggerLabels = {
    collapseTriggerMoreLabel: "checkout.collapseTriggerMore"|trans|sw_sanitize,
    collapseTriggerLessLabel: "checkout.collapseTriggerLess"|trans|sw_sanitize
} %}


{% block component_payment_method %}
    {% if expressCheckoutData and payment.shortName != 'pay_pal_payment_handler' %}
        {#not show another payment#}
    {% else %}
        {{ dump(page.paymentMethods) }}
        {% for payment in page.paymentMethods[:visiblePaymentMethodsLimit] %}
            {% sw_include '@Storefront/storefront/component/payment/payment-method.html.twig' %}
        {% endfor %}

        {% block component_payment_method_collapse %}
            {% if page.paymentMethods | length > visiblePaymentMethodsLimit and visiblePaymentMethodsLimit is not same as(null) %}
                <div class="collapse">
                    {% for payment in page.paymentMethods[visiblePaymentMethodsLimit:] %}
                        {% sw_include '@Storefront/storefront/component/payment/payment-method.html.twig' %}
                    {% endfor %}
                </div>

                {% block component_payment_method_collapse_trigger %}
                    <div class="btn btn-link confirm-checkout-collapse-trigger"
                         data-collapse-checkout-confirm-methods="true"
                         data-collapse-checkout-confirm-methods-options='{{ collapseTriggerLabels|json_encode }}'>
                            <span class="confirm-checkout-collapse-trigger-label">
                                {% block component_payment_method_collapse_trigger_label %}
                                    {{ collapseTriggerLabels.collapseTriggerMoreLabel }}
                                {% endblock %}
                            </span>
                        {% block component_payment_method_collapse_trigger_icon %}
                            {% sw_icon 'arrow-down' style {
                                'class': 'confirm-checkout-chevron',
                                'size': 'xs',
                                'pack' : 'solid'
                            } %}
                        {% endblock %}
                    </div>
                {% endblock %}
            {% endif %}
        {% endblock %}
    {% endif %}

{% endblock %}
