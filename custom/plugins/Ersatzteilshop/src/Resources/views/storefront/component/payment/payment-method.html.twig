{% sw_extends '@Storefront/storefront/component/payment/payment-method.html.twig' %}

{% block component_payment_method_label %}
    <label class="custom-payment-control-label custom-control-label payment-method-label"
       for="paymentMethod{{ payment.id }}">
    {% block component_payment_method_image %}
        <div class="payment-method-wrapper">
        {% if payment.media %}
            {% sw_thumbnails 'payment-method-image-thumbnails' with {
                media: payment.media,
                sizes: {
                    'default': '100px'
                },
                attributes: {
                    'class': 'payment-method-image',
                    'alt': (payment.media.translated.alt ?: payment.translated.name),
                    'title': (payment.media.translated.title ?: payment.translated.name)
                }
            } %}
        {% endif %}
    {% endblock %}

    {% block component_payment_method_description %}
            <span class="payment-method-name">{{ payment.translated.name }}</span>

            <div class="payment-method-toggle-button
                {% if payment.id is same as(defaultPaymentMethodId) %} is-selected {% endif %}">
                {% if payment.id is same as(defaultPaymentMethodId) %}
                    {# Aktiver Pfeil nach unten #}
                    <svg class="payment-method-toggle-icon" xmlns="http://www.w3.org/2000/svg" width="17" height="11" viewBox="0 0 17 11" fill="none">
                        <path d="M14.8828 1.79492L8.47269 8.20504L2.06257 1.79492" stroke="#202E3D" stroke-width="3.84607"/>
                    </svg>
                {% else %}
                    {# Pfeil nach rechts #}
                    <svg class="payment-method-toggle-icon" xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16" fill="none">
                        <path d="M2.26953 1.58984L8.67965 7.99996L2.26953 14.4101" stroke="#989898" stroke-width="3.84607"/>
                    </svg>
                {% endif %}
            </div>
        </div>

        <div class="payment-method-toggle-description
            {% if payment.id is same as(defaultPaymentMethodId) %} is-selected d-none {% else %} d-none {% endif %} k11-mt-15">
            {{ payment.translated.customFields.payment_description|raw }}
        </div>

        {% if not accountPayment %}
            {% if payment.id is same as(defaultPaymentMethodId) %}
                {% if page.extensions.unzerPaymentFrame.paymentFrame or payment.translated.customFields.payment_description %}
                    <div class="payment-method-description">
                        {% if page.extensions.unzerPaymentFrame.paymentFrame %}
                            {% block unzer_payment_checkout_confirm_frame %}
                                {% sw_include '@Storefront/storefront/component/unzer/base/unzer-library.html.twig' %}

                                {% block page_checkout_unzer_payment_form_elements %}
                                    <input type="hidden" id="unzerResourceId" name="unzerResourceId"
                                           form="confirmOrderForm">
                                {% endblock %}

                                {% block unzer_payment_checkout_confirm_frame_card %}

                                    {% if 'paypal' in page.extensions.unzerPaymentFrame.paymentFrame and page.extensions.unzerPayPal.displayPayPalAccountSelection
                                        or 'paypal' not in page.extensions.unzerPaymentFrame.paymentFrame %}
                                        <div class="unzer-payment-card"
                                             id="unzer-payment-card"
                                             data-unzer-payment-base="true"
                                             data-unzer-payment-base-options='{
                                             "publicKey": "{{ page.extensions['unzerPaymentData'].publicKey }}",
                                             "shopLocale": "{{ page.extensions['unzerPaymentData'].locale }}",
                                             "errorShouldNotBeEmpty": "{{ "error.VIOLATION::IS_BLANK_ERROR" | trans }}"
                                             {% if isOrderEdit %}, "isOrderEdit": true {% endif %}
                                         }'>
                                            <div class="card-body">
                                                {% block unzer_payment_checkout_confirm_frame_card_body %}
                                                    {% block unzer_payment_checkout_confirm_frame_card_body_frame %}
                                                        <div class="unzer-payment-frame">
                                                            {% sw_include page.extensions.unzerPaymentFrame.paymentFrame ignore missing %}
                                                        </div>
                                                    {% endblock %}

                                                    {% block unzer_payment_google_pay %}
                                                        {% set googlePayExtensionName = 'unzerGooglePay' %}
                                                        {% if page.extensions[googlePayExtensionName] and payment.id == constant("UnzerPayment6\\Installer\\PaymentInstaller::PAYMENT_ID_GOOGLE_PAY") %}
                                                            {% set googlePayOptions = page.extensions[googlePayExtensionName].publicConfig|merge({
                                                                currency: context.currency.isoCode,
                                                                amount: page.cart.price.rawTotal ?: page.cart.price.totalPrice
                                                            }) %}
                                                            <div data-unzer-payment-google-pay data-unzer-payment-google-pay-options="{{ googlePayOptions|json_encode }}">
                                                                <div id="unzer-google-pay-button"></div>
                                                            </div>
                                                        {% endif %}
                                                    {% endblock %}
                                                {% endblock %}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endblock %}
                            {% endblock %}
                        {% endif %}

                        {{ payment.translated.customFields.payment_description|raw }}
                    </div>
                {% endif %}
            {% endif %}
        {% endif %}
    {% endblock %}
    </label>
{% endblock %}
